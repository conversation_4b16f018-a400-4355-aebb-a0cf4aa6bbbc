# Quick Start Guide

## Ready to Process Your Documents? 

You have everything set up! Here's how to use the batch processor:

### Step 1: Test First (Recommended)
```bash
# Run the pre-flight check
python3 test_script.py

# See what files would be processed (no changes made)
python3 batch_process_docx.py --dry-run
```

### Step 2: Process with Backups (Safest Option)
```bash
# This will create backups and process all .docx files
python3 batch_process_docx.py --backup
```

### Step 3: Check Results
- Look for the `backups/` folder with your original files
- Check `docx_processing.log` for detailed information
- Open a few processed documents to verify the changes

## What Will Happen

For each .docx file in your directory, the script will:

1. **Create a backup** (if you use `--backup`)
2. **Replace header images** with "Picture 1.png"
3. **Remove all footer images**
4. **Keep everything else** exactly the same

## Current Status

✅ **2 .docx files** ready to process:
- BBVA - solicitud copia recargo 1.docx
- <PERSON><PERSON>dor <PERSON> Defender - Nueva imagen 1.docx

✅ **Picture 1.png** found and ready to use

✅ **python-docx library** installed and working

## Need Help?

- Read the full `README.md` for detailed documentation
- Check `docx_processing.log` if something goes wrong
- Use `--dry-run` to preview changes without modifying files

## Emergency Recovery

If something goes wrong and you used `--backup`:
```bash
# Copy backup files back
cp backups/*_backup.docx .

# Remove the "_backup" from filenames manually or with:
for file in *_backup.docx; do mv "$file" "${file/_backup/}"; done
```
